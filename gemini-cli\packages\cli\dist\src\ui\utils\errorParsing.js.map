{"version": 3, "file": "errorParsing.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/errorParsing.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAmB,MAAM,oBAAoB,CAAC;AAE/D,MAAM,+BAA+B,GACnC,uMAAuM,CAAC;AAC1M,MAAM,mCAAmC,GACvC,2IAA2I,CAAC;AAC9I,MAAM,+BAA+B,GACnC,wIAAwI,CAAC;AAC3I,MAAM,gCAAgC,GACpC,sEAAsE,CAAC;AAWzE,SAAS,UAAU,CAAC,KAAc;IAChC,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,OAAO,IAAI,KAAK;QAChB,OAAQ,KAAkB,CAAC,KAAK,KAAK,QAAQ;QAC7C,SAAS,IAAK,KAAkB,CAAC,KAAK,CACvC,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,KAAc;IACvC,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,SAAS,IAAI,KAAK;QAClB,OAAQ,KAAyB,CAAC,OAAO,KAAK,QAAQ,CACvD,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAmB;IAC9C,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ,CAAC,0BAA0B;YACtC,OAAO,+BAA+B,CAAC;QACzC,KAAK,QAAQ,CAAC,UAAU;YACtB,OAAO,mCAAmC,CAAC;QAC7C,KAAK,QAAQ,CAAC,aAAa;YACzB,OAAO,+BAA+B,CAAC;QACzC;YACE,OAAO,gCAAgC,CAAC;IAC5C,CAAC;AACH,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,KAAc,EACd,QAAmB;IAEnB,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,IAAI,IAAI,GAAG,eAAe,KAAK,CAAC,OAAO,GAAG,CAAC;QAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YACzB,IAAI,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gEAAgE;IAChE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YACrB,OAAO,eAAe,KAAK,GAAG,CAAC,CAAC,kCAAkC;QACpE,CAAC;QAED,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE9C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAY,CAAC;YACtD,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5B,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC7C,IAAI,CAAC;oBACH,8DAA8D;oBAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAY,CAAC;oBACxD,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC5B,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC;oBAC3C,CAAC;gBACH,CAAC;gBAAC,OAAO,EAAE,EAAE,CAAC;oBACZ,kEAAkE;gBACpE,CAAC;gBACD,IAAI,IAAI,GAAG,eAAe,YAAY,aAAa,WAAW,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC;gBAChF,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBACnC,IAAI,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACxC,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,kEAAkE;QACpE,CAAC;QACD,OAAO,eAAe,KAAK,GAAG,CAAC;IACjC,CAAC;IAED,OAAO,yCAAyC,CAAC;AACnD,CAAC"}