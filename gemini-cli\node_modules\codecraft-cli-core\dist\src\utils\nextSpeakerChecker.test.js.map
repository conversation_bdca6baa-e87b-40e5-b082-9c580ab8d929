{"version": 3, "file": "nextSpeakerChecker.test.js", "sourceRoot": "", "sources": ["../../../src/utils/nextSpeakerChecker.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAQ,SAAS,EAAE,MAAM,QAAQ,CAAC;AAE/E,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAuB,MAAM,yBAAyB,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,2CAA2C;AAC3C,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC7B,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AAE/B,mFAAmF;AACnF,MAAM,kBAAkB,GAAG;IACzB,eAAe,EAAE,EAAE,CAAC,EAAE,EAAE;IACxB,qBAAqB,EAAE,EAAE,CAAC,EAAE,EAAE;IAC9B,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE;IACpB,YAAY,EAAE,EAAE,CAAC,EAAE,EAAE;IACrB,kBAAkB,EAAE,EAAE,CAAC,EAAE,EAAE;CACP,CAAC;AAEvB,MAAM,uBAAuB,GAAG;IAC9B,kBAAkB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,kBAAkB,CAAC;IAC/D,sGAAsG;CAC7E,CAAC;AAE5B,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;IAClC,MAAM,WAAW,GACf,MAAM,EAAE,CAAC,YAAY,CAAiC,eAAe,CAAC,CAAC;IACzE,OAAO;QACL,GAAG,WAAW;QACd,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,uBAAuB,CAAC,EAAE,qDAAqD;QACxG,6EAA6E;QAC7E,uEAAuE;KACxE,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,YAAwB,CAAC;IAC7B,IAAI,gBAA8B,CAAC;IACnC,IAAI,UAAgB,CAAC;IACrB,MAAM,WAAW,GAAG,IAAI,eAAe,EAAE,CAAC,MAAM,CAAC;IAEjD,UAAU,CAAC,GAAG,EAAE;QACd,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/B,MAAM,kBAAkB,GAAG,IAAI,UAAU,CACvC,cAAc,EACd,YAAY,EACZ,KAAK,EACL,GAAG,EACH,KAAK,EACL,SAAS,EACT,KAAK,EACL,SAAS,EACT,SAAS,EACT,SAAS,CACV,CAAC;QAEF,gBAAgB,GAAG,IAAI,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAExD,wDAAwD;QACxD,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,SAAS,EAAE,CAAC;QAC1D,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC,SAAS,EAAE,CAAC;QAEhE,sFAAsF;QACtF,YAAY,GAAG,IAAI,UAAU,CAC3B,kBAAkB,EAClB,kBAAkB,EAAE,8EAA8E;QAClG,EAAE,EACF,EAAE,CACH,CAAC;QAEF,qCAAqC;QACrC,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,aAAa,EAAE,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;QACrD,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACtD,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC1B,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;QAClE,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC;YAChD,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE;SAChC,CAAC,CAAC;QAChB,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC1B,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;QACrF,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC;YAChD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,0BAA0B,EAAE,CAAC,EAAE;SACpD,CAAC,CAAC;QAChB,MAAM,eAAe,GAAwB;YAC3C,SAAS,EAAE,oCAAoC;YAC/C,YAAY,EAAE,OAAO;SACtB,CAAC;QACD,gBAAgB,CAAC,YAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAE3E,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACxC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;QAChF,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC;YAChD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,4BAA4B,EAAE,CAAC,EAAE;SACtD,CAAC,CAAC;QAChB,MAAM,eAAe,GAAwB;YAC3C,SAAS,EAAE,yBAAyB;YACpC,YAAY,EAAE,MAAM;SACrB,CAAC;QACD,gBAAgB,CAAC,YAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAE3E,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qEAAqE,EAAE,KAAK,IAAI,EAAE;QAClF,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC;YAChD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,CAAC,EAAE;SAChD,CAAC,CAAC;QAChB,MAAM,eAAe,GAAwB;YAC3C,SAAS,EAAE,8CAA8C;YACzD,YAAY,EAAE,MAAM;SACrB,CAAC;QACD,gBAAgB,CAAC,YAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAE3E,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iEAAiE,EAAE,KAAK,IAAI,EAAE;QAC/E,MAAM,cAAc,GAAG,EAAE;aACtB,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;aACtB,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAC/B,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC;YAChD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,EAAE;SAC9C,CAAC,CAAC;QACf,gBAAgB,CAAC,YAAqB,CAAC,iBAAiB,CACvD,IAAI,KAAK,CAAC,WAAW,CAAC,CACvB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC1B,cAAc,CAAC,WAAW,EAAE,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6FAA6F,EAAE,KAAK,IAAI,EAAE;QAC1G,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC;YAChD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,EAAE;SAC9C,CAAC,CAAC;QACf,gBAAgB,CAAC,YAAqB,CAAC,iBAAiB,CAAC;YACxD,SAAS,EAAE,qBAAqB;SACC,CAAC,CAAC,CAAC,8CAA8C;QAEpF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mFAAmF,EAAE,KAAK,IAAI,EAAE;QAChG,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC;YAChD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,EAAE;SAC9C,CAAC,CAAC;QACf,gBAAgB,CAAC,YAAqB,CAAC,iBAAiB,CAAC;YACxD,SAAS,EAAE,8CAA8C;YACzD,YAAY,EAAE,GAAG,EAAE,eAAe;SACD,CAAC,CAAC;QAErC,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8FAA8F,EAAE,KAAK,IAAI,EAAE;QAC3G,YAAY,CAAC,UAAmB,CAAC,eAAe,CAAC;YAChD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,EAAE;SAC9C,CAAC,CAAC;QACf,gBAAgB,CAAC,YAAqB,CAAC,iBAAiB,CAAC;YACxD,SAAS,EAAE,8CAA8C;YACzD,YAAY,EAAE,SAAS,EAAE,qBAAqB;SACb,CAAC,CAAC;QAErC,MAAM,MAAM,GAAG,MAAM,gBAAgB,CACnC,YAAY,EACZ,gBAAgB,EAChB,WAAW,CACZ,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}